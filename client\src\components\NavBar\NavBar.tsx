import React, { useState, useEffect } from 'react';
import { styled } from '@mui/material/styles';
import { Box, Container, Typography, Button, Avatar, IconButton, Drawer, List, ListItem, ListItemButton, ListItemText } from '@mui/material';
import { Menu as MenuIcon, Close as CloseIcon } from '@mui/icons-material';
import { useTranslation } from 'react-i18next';

// Styled Components
interface NavBarContainerProps {
  scrolled?: boolean;
}

const NavBarContainer = styled(Box, {
  shouldForwardProp: (prop) => prop !== 'scrolled',
})<NavBarContainerProps>(({ theme, scrolled }) => ({
  position: 'fixed',
  top: 0,
  left: 0,
  right: 0,
  zIndex: 1100,
  background: scrolled
    ? `linear-gradient(135deg,
        ${theme.palette.primary.main}F5 0%,
        ${theme.palette.secondary.main}F5 50%,
        #9C27B0F5 100%
      )`
    : `linear-gradient(135deg,
        ${theme.palette.primary.main}E6 0%,
        ${theme.palette.secondary.main}E6 50%,
        #9C27B0E6 100%
      )`,
  backdropFilter: 'blur(10px)',
  borderRadius: '0 0 24px 24px',
  margin: '0 16px',
  transition: theme.transitions.create(['background', 'box-shadow'], {
    duration: theme.transitions.duration.short,
  }),
  boxShadow: scrolled ? '0 4px 20px rgba(0, 0, 0, 0.1)' : 'none',
  [theme.breakpoints.down('sm')]: {
    margin: '0 8px',
    borderRadius: '0 0 16px 16px',
  },
}));

const NavBarContent = styled(Container)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'space-between',
  padding: theme.spacing(1.5, 2),
  [theme.breakpoints.down('sm')]: {
    padding: theme.spacing(1, 1.5),
  },
}));

const LogoSection = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  gap: theme.spacing(1),
  cursor: 'pointer',
  transition: theme.transitions.create(['transform'], {
    duration: theme.transitions.duration.short,
  }),
  '&:hover': {
    transform: 'scale(1.02)',
  },
}));

const LogoAvatar = styled(Avatar)(() => ({
  width: 32,
  height: 32,
  backgroundColor: 'rgba(255, 255, 255, 0.2)',
  border: '2px solid rgba(255, 255, 255, 0.3)',
  '& img': {
    width: '100%',
    height: '100%',
    objectFit: 'contain',
  },
}));

const NavLinks = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  gap: theme.spacing(1),
  [theme.breakpoints.down('md')]: {
    display: 'none',
  },
}));

const MobileMenuButton = styled(IconButton)(({ theme }) => ({
  color: 'white',
  display: 'none',
  [theme.breakpoints.down('md')]: {
    display: 'flex',
  },
}));

const MobileDrawer = styled(Drawer)(({ theme }) => ({
  '& .MuiDrawer-paper': {
    width: 280,
    background: `linear-gradient(135deg,
      ${theme.palette.primary.main}F0 0%,
      ${theme.palette.secondary.main}F0 50%,
      #9C27B0F0 100%
    )`,
    backdropFilter: 'blur(10px)',
    color: 'white',
  },
}));

const MobileNavList = styled(List)(({ theme }) => ({
  padding: theme.spacing(2, 0),
}));

const MobileNavItem = styled(ListItemButton)(({ theme }) => ({
  margin: theme.spacing(0.5, 2),
  borderRadius: theme.spacing(1),
  '&:hover': {
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
  },
}));

const NavLink = styled(Button)(({ theme }) => ({
  color: 'white',
  textTransform: 'none',
  fontWeight: 500,
  fontSize: '0.95rem',
  padding: theme.spacing(0.75, 1.5),
  borderRadius: theme.spacing(2),
  minWidth: 'auto',
  transition: theme.transitions.create(['background-color', 'transform'], {
    duration: theme.transitions.duration.short,
  }),
  '&:hover': {
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
    transform: 'translateY(-1px)',
  },
  '&:active': {
    transform: 'translateY(0)',
  },
}));

const ProfileSection = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  gap: theme.spacing(1),
}));

const ProfileAvatar = styled(Avatar)(({ theme }) => ({
  width: 36,
  height: 36,
  backgroundColor: 'rgba(255, 255, 255, 0.2)',
  border: '2px solid rgba(255, 255, 255, 0.3)',
  cursor: 'pointer',
  transition: theme.transitions.create(['transform', 'box-shadow'], {
    duration: theme.transitions.duration.short,
  }),
  '&:hover': {
    transform: 'scale(1.05)',
    boxShadow: '0 4px 12px rgba(255, 255, 255, 0.3)',
  },
}));

const BrandText = styled(Typography)(({ theme }) => ({
  color: 'white',
  fontWeight: 600,
  fontSize: '1.1rem',
  [theme.breakpoints.down('sm')]: {
    fontSize: '1rem',
  },
}));

// Navigation items configuration
const navigationItems = [
  { key: 'home', labelKey: 'nav.home' },
  { key: 'about', labelKey: 'nav.about' },
  { key: 'skills', labelKey: 'nav.skills' },
  { key: 'projects', labelKey: 'nav.projects' },
  { key: 'blogs', labelKey: 'nav.blogs' },
  { key: 'contact', labelKey: 'nav.contact' },
];

interface NavBarProps {
  onNavigate?: (section: string) => void;
  onProfileClick?: () => void;
}

const NavBar: React.FC<NavBarProps> = ({ onNavigate, onProfileClick }) => {
  const { t } = useTranslation();
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [scrolled, setScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      const isScrolled = window.scrollY > 50;
      setScrolled(isScrolled);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const handleNavClick = (section: string) => {
    setMobileMenuOpen(false); // Close mobile menu when navigating
    if (onNavigate) {
      onNavigate(section);
    } else {
      // Default behavior: scroll to section
      const element = document.getElementById(`${section}-section`);
      if (element) {
        element.scrollIntoView({ behavior: 'smooth', block: 'start' });
      }
    }
  };

  const handleLogoClick = () => {
    setMobileMenuOpen(false);
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const toggleMobileMenu = () => {
    setMobileMenuOpen(!mobileMenuOpen);
  };

  return (
    <NavBarContainer scrolled={scrolled}>
      <NavBarContent maxWidth="lg">
        <LogoSection onClick={handleLogoClick}>
          <LogoAvatar>
            <img src="/Pigeon Squad Logo.png" alt="Logo" />
          </LogoAvatar>
          <BrandText variant="h6">
            {t('app.title')}
          </BrandText>
        </LogoSection>

        <NavLinks>
          {navigationItems.map((item) => (
            <NavLink
              key={item.key}
              onClick={() => handleNavClick(item.key)}
            >
              {t(item.labelKey)}
            </NavLink>
          ))}
        </NavLinks>

        <ProfileSection>
          <MobileMenuButton onClick={toggleMobileMenu}>
            <MenuIcon />
          </MobileMenuButton>
          <ProfileAvatar onClick={onProfileClick}>
            <img src="/Pigeon Squad Logo.png" alt="Profile" />
          </ProfileAvatar>
        </ProfileSection>
      </NavBarContent>

      {/* Mobile Navigation Drawer */}
      <MobileDrawer
        anchor="right"
        open={mobileMenuOpen}
        onClose={() => setMobileMenuOpen(false)}
      >
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', p: 2 }}>
          <Typography variant="h6" sx={{ color: 'white', fontWeight: 600 }}>
            {t('app.title')}
          </Typography>
          <IconButton onClick={() => setMobileMenuOpen(false)} sx={{ color: 'white' }}>
            <CloseIcon />
          </IconButton>
        </Box>
        <MobileNavList>
          {navigationItems.map((item) => (
            <ListItem key={item.key} disablePadding>
              <MobileNavItem onClick={() => handleNavClick(item.key)}>
                <ListItemText
                  primary={t(item.labelKey)}
                  sx={{
                    '& .MuiListItemText-primary': {
                      fontWeight: 500,
                      fontSize: '1rem'
                    }
                  }}
                />
              </MobileNavItem>
            </ListItem>
          ))}
        </MobileNavList>
      </MobileDrawer>
    </NavBarContainer>
  );
};

export default NavBar;
